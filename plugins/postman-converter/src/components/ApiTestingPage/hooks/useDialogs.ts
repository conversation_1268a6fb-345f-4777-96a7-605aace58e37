import { useState } from 'react';

export interface RenameDialogData {
  itemType: 'collection' | 'folder' | 'request';
  itemId: string;
  collectionId: string;
  currentName: string;
}

export const useDialogs = () => {
  // Dialog states
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState(false);
  const [createRequestDialogOpen, setCreateRequestDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [renameDialogData, setRenameDialogData] = useState<RenameDialogData | null>(null);
  const [selectedCollectionForAction, setSelectedCollectionForAction] = useState<string>('');
  const [selectedFolderForAction, setSelectedFolderForAction] = useState<string>('');
  const [editDialogOpen, setEditDialogOpen] = useState<boolean>(false);
  const [editCollectionId, setEditCollectionId] = useState<string>('');
  const [editCollectionName, setEditCollectionName] = useState<string>('');
  const [editCollectionDescription, setEditCollectionDescription] = useState<string>('');
  const [importDialogOpen, setImportDialogOpen] = useState<boolean>(false);
  const [exportDialogOpen, setExportDialogOpen] = useState<boolean>(false);

  // Dialog handlers
  const openCreateFolderDialog = (collectionId: string, parentFolderId?: string) => {
    setSelectedCollectionForAction(collectionId);
    setSelectedFolderForAction(parentFolderId || '');
    setCreateFolderDialogOpen(true);
  };

  const closeCreateFolderDialog = () => {
    setCreateFolderDialogOpen(false);
    setSelectedCollectionForAction('');
    setSelectedFolderForAction('');
  };

  const openCreateRequestDialog = (collectionId: string, folderId?: string) => {
    setSelectedCollectionForAction(collectionId);
    setSelectedFolderForAction(folderId || '');
    setCreateRequestDialogOpen(true);
  };

  const closeCreateRequestDialog = () => {
    setCreateRequestDialogOpen(false);
    setSelectedCollectionForAction('');
    setSelectedFolderForAction('');
  };

  const openRenameDialog = (data: RenameDialogData) => {
    setRenameDialogData(data);
    setRenameDialogOpen(true);
  };

  const closeRenameDialog = () => {
    setRenameDialogOpen(false);
    setRenameDialogData(null);
  };

  const openEditDialog = (collectionId: string, name: string, description: string) => {
    setEditCollectionId(collectionId);
    setEditCollectionName(name);
    setEditCollectionDescription(description);
    setEditDialogOpen(true);
  };

  const closeEditDialog = () => {
    setEditDialogOpen(false);
    setEditCollectionId('');
    setEditCollectionName('');
    setEditCollectionDescription('');
  };

  const openImportDialog = () => {
    setImportDialogOpen(true);
  };

  const closeImportDialog = () => {
    setImportDialogOpen(false);
  };

  const openExportDialog = () => {
    setExportDialogOpen(true);
  };

  const closeExportDialog = () => {
    setExportDialogOpen(false);
  };

  return {
    // States
    createFolderDialogOpen,
    createRequestDialogOpen,
    renameDialogOpen,
    renameDialogData,
    selectedCollectionForAction,
    selectedFolderForAction,
    editDialogOpen,
    editCollectionId,
    editCollectionName,
    editCollectionDescription,
    importDialogOpen,
    exportDialogOpen,
    
    // Handlers
    openCreateFolderDialog,
    closeCreateFolderDialog,
    openCreateRequestDialog,
    closeCreateRequestDialog,
    openRenameDialog,
    closeRenameDialog,
    openEditDialog,
    closeEditDialog,
    openImportDialog,
    closeImportDialog,
    openExportDialog,
    closeExportDialog,
    
    // Setters for controlled components
    setEditCollectionName,
    setEditCollectionDescription,
  };
};
