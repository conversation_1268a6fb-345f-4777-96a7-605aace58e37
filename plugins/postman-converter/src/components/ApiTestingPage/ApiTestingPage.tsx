import { FC } from 'react';
import {
  Grid,
  Typography,
  makeStyles,
  Box,
  Snackbar,
} from '@material-ui/core';
import {
  InfoCard,
} from '@backstage/core-components';
import { Alert } from '@material-ui/lab';

// Custom hooks
import { useCollections } from './hooks/useCollections';
import { useEnvironments } from './hooks/useEnvironments';
import { useRequest } from './hooks/useRequest';
import { useDialogs } from './hooks/useDialogs';
import { useSnackbar } from './hooks/useSnackbar';

// Components
import { CollectionsSidebar } from './components/CollectionsSidebar';
import { RequestPanel } from './components/RequestPanel';
import { ResponsePanel } from './components/ResponsePanel';
import { EnvironmentSelector } from './components/EnvironmentSelector';
import { ImportDialog } from './ImportDialog';
import { ExportDialog } from './ExportDialog';
import { CreateFolderDialog } from './CreateFolderDialog';
import { CreateRequestDialog } from './CreateRequestDialog';
import { RenameDialog } from './components/RenameDialog';

// Icons
import TestingIcon from '@material-ui/icons/BugReport';

// Types
import { ApiCollection, ApiEnvironment } from '../../types';

const useStyles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(3),
    backgroundColor: theme.palette.background.default,
    minHeight: '100vh',
  },
  infoCard: {
    marginBottom: theme.spacing(3),
  },
}));

export const ApiTestingPage = () => {
  const classes = useStyles();

  // Custom hooks
  const {
    collections,
    setCollections,
    collectionsLoading,
    collectionsError,
    expandedFolders,
    selectedItemId,
    handleFolderToggle,
    handleItemSelect,
    handleAddCollection,
    handleDeleteCollection,
    handleImportCollection,
    handleAddFolder,
    handleAddRequest,
    handleRenameCollection,
    handleRenameFolder,
    handleRenameRequest,
  } = useCollections();

  const {
    environments,
    currentEnvironment,
    handleAddEnvironment,
    handleEditEnvironment,
    handleImportEnvironment,
    setCurrentEnvironment,
  } = useEnvironments();

  const {
    currentRequest,
    setCurrentRequest,
    currentResponse,
    isLoading,
    tabValue,
    responseTabValue,
    isGeneratingTests,
    isRunningTests,
    testResults,
    testError,
    isSavingPreRequestScript,
    preRequestScriptError,
    handleTabChange,
    handleResponseTabChange,
    handleMethodChange,
    handleUrlChange,
    handleSendRequest,
    handleGenerateTests,
    handleRunTests,
    handleSaveTests,
    handleSavePreRequestScript,
  } = useRequest(collections, setCollections);

  const {
    createFolderDialogOpen,
    createRequestDialogOpen,
    renameDialogOpen,
    renameDialogData,
    selectedCollectionForAction,
    selectedFolderForAction,
    importDialogOpen,
    exportDialogOpen,
    closeCreateFolderDialog,
    closeCreateRequestDialog,
    closeRenameDialog,
    openImportDialog,
    closeImportDialog,
    closeExportDialog,
  } = useDialogs();

  const { snackbar, showSnackbar, hideSnackbar } = useSnackbar();

  // Handle import collection wrapper
  const handleImportCollectionWrapper = (apiCollection: ApiCollection) => {
    handleImportCollection(apiCollection);
    showSnackbar(`Collection "${apiCollection.name}" imported successfully`, 'success');
  };

  // Handle environment change
  const handleEnvironmentChange = (environmentId: string) => {
    setCurrentEnvironment(environmentId);
  };

  return (
    <div className={classes.root}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <InfoCard
            title={
              <Box display="flex" alignItems="center">
                <TestingIcon style={{ marginRight: '8px' }} />
                API Testing
              </Box>
            }
            className={classes.infoCard}
          >
            <Typography variant="body1" paragraph>
              Test your APIs by sending requests and viewing responses. Organize your requests into collections and use environments to manage variables.
            </Typography>
          </InfoCard>
        </Grid>

        {/* Main content grid */}
        <Grid container item xs={12} spacing={3}>
          {/* Collections sidebar */}
          <Grid item xs={12} md={3}>
            <CollectionsSidebar
              collections={collections}
              collectionsLoading={collectionsLoading}
              collectionsError={collectionsError}
              expandedFolders={expandedFolders}
              selectedItemId={selectedItemId}
              onFolderToggle={handleFolderToggle}
              onItemSelect={handleItemSelect}
              onAddCollection={handleAddCollection}
              onDeleteCollection={handleDeleteCollection}
              onEditCollection={() => {}} // TODO: Implement edit collection
              onAddFolder={handleAddFolder}
              onAddRequest={handleAddRequest}
              onDeleteRequest={() => {}} // TODO: Implement delete request
              onDeleteFolder={() => {}} // TODO: Implement delete folder
              onDuplicateRequest={() => {}} // TODO: Implement duplicate request
              onRenameCollection={handleRenameCollection}
              onRenameFolder={handleRenameFolder}
              onRenameRequest={handleRenameRequest}
            />
          </Grid>

          {/* Request and response area */}
          <Grid item xs={12} md={9}>
            {/* Environment selector */}
            <EnvironmentSelector
              environments={environments}
              currentEnvironment={currentEnvironment}
              onEnvironmentChange={handleEnvironmentChange}
              onAddEnvironment={handleAddEnvironment}
              onEditEnvironment={handleEditEnvironment}
              onImportEnvironment={() => openImportDialog()}
            />

            {/* Request panel */}
            <RequestPanel
              request={currentRequest}
              currentEnvironment={environments.find(env => env.id === currentEnvironment)}
              isLoading={isLoading}
              tabValue={tabValue}
              testResults={testResults}
              testError={testError}
              isGeneratingTests={isGeneratingTests}
              isRunningTests={isRunningTests}
              isSavingPreRequestScript={isSavingPreRequestScript}
              preRequestScriptError={preRequestScriptError}
              onTabChange={handleTabChange}
              onMethodChange={handleMethodChange}
              onUrlChange={handleUrlChange}
              onSendRequest={handleSendRequest}
              onUpdateRequest={setCurrentRequest}
              onGenerateTests={handleGenerateTests}
              onRunTests={handleRunTests}
              onSaveTests={handleSaveTests}
              onSavePreRequestScript={handleSavePreRequestScript}
              onSaveRequest={() => {}} // TODO: Implement save request
              response={currentResponse}
            />

            {/* Response panel */}
            {currentResponse && (
              <ResponsePanel
                response={currentResponse}
                tabValue={responseTabValue}
                onTabChange={handleResponseTabChange}
              />
            )}
          </Grid>
        </Grid>
      </Grid>

      {/* Dialogs */}
      <ImportDialog
        open={importDialogOpen}
        onClose={closeImportDialog}
        onImportCollection={handleImportCollectionWrapper}
        onImportEnvironment={handleImportEnvironment}
      />

      <ExportDialog
        open={exportDialogOpen}
        onClose={closeExportDialog}
        collections={collections}
        environments={environments}
      />

      <CreateFolderDialog
        open={createFolderDialogOpen}
        onClose={closeCreateFolderDialog}
        collections={collections}
        selectedCollectionId={selectedCollectionForAction}
        selectedFolderId={selectedFolderForAction}
        onCreateFolder={(folderName, parentId, collectionId) => {
          // Handle folder creation
          handleAddFolder(collectionId, parentId || undefined, folderName);
          showSnackbar('Folder created successfully', 'success');
          closeCreateFolderDialog();
        }}
      />

      <CreateRequestDialog
        open={createRequestDialogOpen}
        onClose={closeCreateRequestDialog}
        collections={collections}
        selectedCollectionId={selectedCollectionForAction}
        selectedFolderId={selectedFolderForAction}
        onCreateRequest={(requestName, method, url, parentId, collectionId) => {
          // Handle request creation
          handleAddRequest(collectionId, parentId || undefined, requestName, method, url);
          showSnackbar('Request created successfully', 'success');
          closeCreateRequestDialog();
        }}
      />

      <RenameDialog
        open={renameDialogOpen}
        onClose={closeRenameDialog}
        itemType={renameDialogData?.itemType || 'collection'}
        currentName={renameDialogData?.currentName || ''}
        onRename={(newName) => {
          if (renameDialogData) {
            if (renameDialogData.itemType === 'collection') {
              handleRenameCollection(renameDialogData.itemId, newName);
            } else if (renameDialogData.itemType === 'folder') {
              handleRenameFolder(renameDialogData.collectionId, renameDialogData.itemId, newName);
            } else if (renameDialogData.itemType === 'request') {
              handleRenameRequest(renameDialogData.collectionId, renameDialogData.itemId, newName);
            }
          }
          showSnackbar('Item renamed successfully', 'success');
          closeRenameDialog();
        }}
      />

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={hideSnackbar}
      >
        <Alert onClose={hideSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};
